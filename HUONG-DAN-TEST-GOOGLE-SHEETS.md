# Hướng dẫn test tính năng Google Sheets

## 🎯 Mục tiêu

<PERSON>ểm tra tính năng tích hợp Google Sheets mới được bổ sung vào ESP32, cho phép tự động cập nhật dữ liệu điểm danh lên Google Sheets theo thời gian thực.

## 📋 Chuẩn bị

### 1. <PERSON><PERSON><PERSON> cứng ESP32
- Đảm bảo ESP32 đã được nạp code mới từ file `script/esp32_smart_classroom.ino`
- ESP32 phải kết nối WiFi thành công
- ESP32 phải kết nối Firebase thành công

### 2. Google Sheets
- **Spreadsheet ID**: `1TKl9Zv5HLesK8vLozcb0613mZ2EA093zkz5JnM7UmXY`
- **Link trực tiếp**: https://docs.google.com/spreadsheets/d/1TKl9Zv5HLesK8vLozcb0613mZ2EA093zkz5JnM7UmXY/edit
- Có 2 sheets:
  - **DANHSACH**: <PERSON><PERSON> sách sinh viên
  - **DIEMDANH**: Dữ liệu điểm danh

### 3. Môi trường test
- Node.js đã được cài đặt
- Dependencies đã được cài đặt trong thư mục `hardware-simulator`

## 🚀 Các bước test

### Bước 1: Chạy script test tự động

```bash
# Di chuyển vào thư mục test
cd test

# Chạy script test
node test-google-sheets.js
```

Script này sẽ:
- Mô phỏng điểm danh cho sinh viên Phúc du (RFID: F7C2453)
- Mô phỏng điểm danh cho sinh viên First Tâm (RFID: DDEF412)
- Mô phỏng điểm danh ngẫu nhiên

### Bước 2: Chạy test thủ công

```bash
# Di chuyển vào thư mục hardware-simulator
cd hardware-simulator

# Test điểm danh vào lớp
node simulate-attendance.js checkin F7C2453

# Test điểm danh ra về
node simulate-attendance.js checkout F7C2453

# Test điểm danh ngẫu nhiên
node simulate-attendance.js random
```

### Bước 3: Kiểm tra kết quả

1. **Mở Google Sheets**:
   - Truy cập: https://docs.google.com/spreadsheets/d/1TKl9Zv5HLesK8vLozcb0613mZ2EA093zkz5JnM7UmXY/edit

2. **Kiểm tra sheet DIEMDANH**:
   - Cột A: Ngày (format YYYYMMDD, ví dụ: 20241210)
   - Cột B: Tên sinh viên (ví dụ: Phúc du)
   - Cột C: Mã sinh viên (ví dụ: **********)
   - Cột D: Mã RFID (ví dụ: F7C2453)
   - Cột E: Giờ vào (ví dụ: 08:30:15)
   - Cột F: Giờ ra (có thể trống nếu chưa điểm danh ra)
   - Cột G: Trạng thái (present)

3. **Kiểm tra sheet DANHSACH**:
   - Cột A: Mã RFID (ví dụ: F7C2453)
   - Cột B: Tên sinh viên (ví dụ: Phúc du)
   - Cột C: Mã sinh viên (ví dụ: **********)
   - Cột D: Lớp (ví dụ: 2021DHKTMT01)
   - Cột E: Ngành (ví dụ: KTMT)

4. **Kiểm tra Serial Monitor ESP32**:
   - Mở Arduino IDE Serial Monitor
   - Tìm các log như:
     ```
     ----- Bắt đầu gửi dữ liệu lên Google Sheets -----
     📊 Sinh viên: Phúc du (ID: **********, RFID: F7C2453)
     📤 Đang gửi dữ liệu lên Google Sheets...
     📊 Mã phản hồi HTTP: 200
     ✅ Gửi dữ liệu lên Google Sheets thành công!
     ```

## ✅ Kết quả mong đợi

### Thành công
- Dữ liệu điểm danh xuất hiện trong sheet DIEMDANH
- Thông tin sinh viên xuất hiện trong sheet DANHSACH
- Serial Monitor hiển thị HTTP 200 và thông báo thành công
- Dữ liệu được cập nhật trong vòng 10-30 giây sau khi chạy script

### Lỗi thường gặp

1. **HTTP 400 (Bad Request)**:
   - Kiểm tra format dữ liệu JSON
   - Kiểm tra tên sheet (DANHSACH, DIEMDANH)

2. **HTTP 401 (Unauthorized)**:
   - Kiểm tra API key trong code ESP32
   - Đảm bảo API key có quyền truy cập Google Sheets

3. **HTTP 403 (Forbidden)**:
   - Kiểm tra quyền của API key
   - Đảm bảo Google Sheets API được bật

4. **HTTP 404 (Not Found)**:
   - Kiểm tra Spreadsheet ID
   - Kiểm tra tên sheet

## 🔧 Debug

### Kiểm tra log ESP32
```
// Tìm các log này trong Serial Monitor:
📊 ESP32 sẽ tự động cập nhật dữ liệu lên Google Sheets
🔗 URL: https://sheets.googleapis.com/v4/spreadsheets/...
📄 Payload: {"values":[["20241210","Phúc du","**********"...]]}
📊 Mã phản hồi HTTP: 200
```

### Kiểm tra Firebase
- Đảm bảo dữ liệu được cập nhật trong Firebase trước
- ESP32 chỉ gửi lên Google Sheets sau khi Firebase thành công

### Kiểm tra kết nối
- WiFi phải ổn định
- ESP32 phải có thể truy cập internet
- Firewall không chặn kết nối HTTPS

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra Serial Monitor để xem log chi tiết
2. Kiểm tra kết nối WiFi và Firebase
3. Đảm bảo Google Sheets có quyền truy cập công khai
4. Kiểm tra API key và Spreadsheet ID

## 🎉 Kết luận

Tính năng Google Sheets hoạt động thành công khi:
- Dữ liệu xuất hiện trong cả 2 sheets
- Serial Monitor hiển thị HTTP 200
- Không có lỗi trong quá trình gửi dữ liệu
- Thời gian cập nhật nhanh (dưới 30 giây)
