# LƯU ĐỒ THUẬT TOÁN ỨNG DỤNG LỚP HỌC THÔNG MINH

## 1. Lưu đồ tổng quan hệ thống

```
┌─────────────────┐
│                 │
│      Bắt đầu    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Khởi tạo ứng   │
│  dụng React     │
│  Native với Expo│
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Kết nối với    │
│  Firebase        │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Khởi tạo các   │
│  Context và     │
│  Provider       │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Khởi tạo       │
│  Navigation     │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Lắng nghe sự   │
│  kiện từ        │
│  Firebase        │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Hiển thị giao  │
│  diện người dùng│
└─────────────────┘
```

## 2. Lưu đồ luồng dữ liệu Firebase

```
┌─────────────────┐
│                 │
│  ESP32/Hardware │
│  Simulator      │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│    Firebase     │
│  Realtime DB    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│  Ứng dụng       │
│  React Native   │
│                 │
└─────────────────┘
```

## 3. Lưu đồ luồng dữ liệu cảm biến

```
┌─────────────────┐
│                 │
│      Bắt đầu    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  ESP32 đọc dữ   │
│  liệu từ cảm    │
│  biến           │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Kiểm tra giá   │
│  trị cảm biến   │
│  có vượt ngưỡng │
└────────┬────────┘
         │
         ▼
     ┌───┴───┐
     │       │
     ▼       ▼
┌─────────┐ ┌─────────┐
│  Không  │ │   Có    │
└────┬────┘ └────┬────┘
     │           │
     │           ▼
     │     ┌─────────────┐
     │     │ Tạo cảnh báo│
     │     │ trên Firebase│
     │     └──────┬──────┘
     │            │
     ▼            ▼
┌─────────────────────────┐
│ Cập nhật dữ liệu cảm biến│
│ lên Firebase            │
└────────────┬────────────┘
             │
             ▼
┌─────────────────────────┐
│ Ứng dụng nhận thông báo │
│ và cập nhật giao diện   │
└────────────┬────────────┘
             │
             ▼
┌─────────────────────────┐
│ Hiển thị dữ liệu cảm biến│
│ trên màn hình Sensors   │
└────────────┬────────────┘
             │
             ▼
        ┌────┴────┐
        │         │
        ▼         ▼
┌─────────────┐ ┌─────────────┐
│ Không có    │ │ Có cảnh báo │
│ cảnh báo    │ │             │
└─────────────┘ └──────┬──────┘
                       │
                       ▼
               ┌─────────────┐
               │ Hiển thị    │
               │ thông báo   │
               └─────────────┘
```

## 4. Lưu đồ hệ thống điểm danh

```
┌─────────────────┐
│                 │
│      Bắt đầu    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Học sinh quẹt  │
│  thẻ RFID       │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  ESP32 đọc mã   │
│  RFID           │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Kiểm tra mã    │
│  RFID có trong  │
│  danh sách      │
└────────┬────────┘
         │
         ▼
     ┌───┴───┐
     │       │
     ▼       ▼
┌─────────┐ ┌─────────┐
│  Không  │ │   Có    │
└────┬────┘ └────┬────┘
     │           │
     ▼           ▼
┌─────────┐ ┌─────────────┐
│ Lưu vào │ │ Kiểm tra đã │
│ danh    │ │ điểm danh   │
│ sách    │ │ chưa        │
│ chưa    │ └──────┬──────┘
│ đăng ký │        │
└─────────┘        ▼
                ┌───┴───┐
                │       │
                ▼       ▼
           ┌─────────┐ ┌─────────┐
           │  Chưa   │ │   Rồi   │
           └────┬────┘ └────┬────┘
                │           │
                ▼           ▼
        ┌─────────────┐ ┌─────────────┐
        │ Ghi nhận    │ │ Kiểm tra    │
        │ thời gian   │ │ thời gian   │
        │ vào         │ │ ra          │
        └──────┬──────┘ └──────┬──────┘
               │               │
               ▼               ▼
        ┌─────────────┐ ┌─────────────┐
        │ Cập nhật    │ │ Cập nhật    │
        │ Firebase    │ │ Firebase    │
        └──────┬──────┘ └──────┬──────┘
               │               │
               └───────┬───────┘
                       │
                       ▼
               ┌─────────────┐
               │ Ứng dụng    │
               │ cập nhật    │
               │ màn hình    │
               │ Attendance  │
               └─────────────┘
```

## 5. Lưu đồ điều khiển thiết bị

```
┌─────────────────┐
│                 │
│      Bắt đầu    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Người dùng     │
│  tương tác với  │
│  màn hình       │
│  Devices        │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Kiểm tra chế   │
│  độ tự động     │
└────────┬────────┘
         │
         ▼
     ┌───┴───┐
     │       │
     ▼       ▼
┌─────────┐ ┌─────────┐
│  Tắt    │ │   Bật   │
└────┬────┘ └────┬────┘
     │           │
     ▼           ▼
┌─────────────┐ ┌─────────────┐
│ Gửi lệnh    │ │ Hiển thị    │
│ điều khiển  │ │ thông báo   │
│ đến Firebase│ │ chế độ tự   │
└──────┬──────┘ │ động đang   │
       │        │ hoạt động   │
       │        └─────────────┘
       │
       ▼
┌─────────────────┐
│  ESP32 lắng     │
│  nghe thay đổi  │
│  từ Firebase    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  ESP32 điều     │
│  khiển thiết bị │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  ESP32 cập nhật │
│  trạng thái     │
│  thực tế lên    │
│  Firebase       │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Ứng dụng cập   │
│  nhật giao diện │
│  theo trạng     │
│  thái thực tế   │
└─────────────────┘
```

## 6. Lưu đồ hệ thống cảnh báo

```
┌─────────────────┐
│                 │
│      Bắt đầu    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Phát hiện giá  │
│  trị cảm biến   │
│  vượt ngưỡng    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Tạo cảnh báo   │
│  trên Firebase  │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Ứng dụng lắng  │
│  nghe cảnh báo  │
│  từ Firebase    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Kiểm tra cài   │
│  đặt thông báo  │
└────────┬────────┘
         │
         ▼
     ┌───┴───┐
     │       │
     ▼       ▼
┌─────────┐ ┌─────────┐
│  Tắt    │ │   Bật   │
└─────────┘ └────┬────┘
                 │
                 ▼
         ┌─────────────┐
         │ Hiển thị    │
         │ thông báo   │
         │ trên thiết  │
         │ bị          │
         └──────┬──────┘
                │
                ▼
         ┌─────────────┐
         │ Cập nhật    │
         │ màn hình    │
         │ Alerts      │
         └──────┬──────┘
                │
                ▼
         ┌─────────────┐
         │ Người dùng  │
         │ xử lý cảnh  │
         │ báo         │
         └──────┬──────┘
                │
                ▼
         ┌─────────────┐
         │ Cập nhật    │
         │ trạng thái  │
         │ cảnh báo    │
         │ trên Firebase│
         └─────────────┘
```

## 7. Lưu đồ chế độ tự động

```
┌─────────────────┐
│                 │
│      Bắt đầu    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Kiểm tra chế   │
│  độ tự động     │
└────────┬────────┘
         │
         ▼
     ┌───┴───┐
     │       │
     ▼       ▼
┌─────────┐ ┌─────────┐
│  Tắt    │ │   Bật   │
└────┬────┘ └────┬────┘
     │           │
     │           ▼
     │    ┌─────────────┐
     │    │ Kiểm tra    │
     │    │ cảm biến    │
     │    │ chuyển động │
     │    └──────┬──────┘
     │           │
     │           ▼
     │        ┌───┴───┐
     │        │       │
     │        ▼       ▼
     │   ┌─────────┐ ┌─────────┐
     │   │  Không  │ │   Có    │
     │   └────┬────┘ └────┬────┘
     │        │           │
     │        ▼           ▼
     │   ┌─────────┐ ┌─────────┐
     │   │ Tắt đèn │ │ Bật đèn │
     │   └─────────┘ └─────────┘
     │
     ▼
┌─────────────────┐
│  Điều khiển     │
│  thủ công       │
└─────────────────┘
```
