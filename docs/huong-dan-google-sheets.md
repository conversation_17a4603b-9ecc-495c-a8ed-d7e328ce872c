# Hướng dẫn tích hợp Google Sheets

## 1. T<PERSON><PERSON> quan

Tính năng tích hợp Google Sheets cho phép ESP32 tự động cập nhật dữ liệu điểm danh và danh sách sinh viên lên Google Sheets theo thời gian thực. Điều này giúp giáo viên và quản lý có thể theo dõi điểm danh trực tiếp trên Google Sheets mà không cần truy cập vào ứng dụng di động.

## 2. <PERSON><PERSON>u hình Google Sheets

### 2.1. Thông tin Spreadsheet

- **Spreadsheet ID**: `1oPFi8-iWNw-BIFbXYCpJq5wcmyR_DeHbpV7azTgMRBQ`
- **Sheet DANHSACH**: Chứa danh sách sinh viên
- **Sheet DIEMDANH**: Chứa dữ liệu điểm danh theo thời gian thực

### 2.2. <PERSON><PERSON><PERSON> trúc dữ liệu

#### Sheet DANHSACH (Danh sách sinh viên)
| Cột A | Cột B | Cột C | Cột D | Cột E |
|-------|-------|-------|-------|-------|
| Mã RFID | Tên sinh viên | Mã sinh viên | Lớp | Ngành |

#### Sheet DIEMDANH (Dữ liệu điểm danh)
| Cột A | Cột B | Cột C | Cột D | Cột E | Cột F | Cột G |
|-------|-------|-------|-------|-------|-------|-------|
| Ngày | Tên sinh viên | Mã sinh viên | Mã RFID | Giờ vào | Giờ ra | Trạng thái |

## 3. Cách hoạt động

### 3.1. Luồng xử lý

1. **Quẹt thẻ RFID**: Sinh viên quẹt thẻ RFID vào đầu đọc
2. **Xử lý ESP32**: ESP32 xác thực thẻ và cập nhật Firebase
3. **Cập nhật Firebase**: Dữ liệu điểm danh được lưu vào Firebase Realtime Database
4. **Cập nhật Google Sheets**: ESP32 tự động gửi dữ liệu lên Google Sheets qua API
5. **Đồng bộ danh sách**: Thông tin sinh viên cũng được cập nhật vào sheet DANHSACH

### 3.2. API được sử dụng

- **Google Sheets API v4**: Để thêm dữ liệu vào spreadsheet
- **Endpoint**: `https://sheets.googleapis.com/v4/spreadsheets/{SPREADSHEET_ID}/values/{SHEET_NAME}:append`
- **Phương thức**: POST với JSON payload
- **Xác thực**: API Key (sử dụng chung với Firebase)

## 4. Cấu hình trong code ESP32

### 4.1. Các hằng số cấu hình

```cpp
// Cấu hình Google Sheets
#define GOOGLE_SHEETS_ID "1oPFi8-iWNw-BIFbXYCpJq5wcmyR_DeHbpV7azTgMRBQ"
#define GOOGLE_SHEETS_API_KEY "AIzaSyAxAR_UUEaXdJl7SMo8vhbPcDcLvvGSM0w"
const char* googleSheetsHost = "sheets.googleapis.com";
const int googleSheetsPort = 443;
```

### 4.2. Các hàm chính

- `sendToGoogleSheets()`: Gửi dữ liệu điểm danh lên sheet DIEMDANH
- `updateStudentListSheet()`: Cập nhật danh sách sinh viên lên sheet DANHSACH

## 5. Xử lý lỗi

### 5.1. Các mã lỗi HTTP phổ biến

- **400 Bad Request**: Yêu cầu không hợp lệ - kiểm tra format dữ liệu
- **401 Unauthorized**: Không có quyền truy cập - kiểm tra API key
- **403 Forbidden**: Bị cấm truy cập - kiểm tra quyền của API key
- **404 Not Found**: Không tìm thấy - kiểm tra Spreadsheet ID và tên sheet

### 5.2. Xử lý khi offline

- Nếu không có kết nối WiFi, dữ liệu vẫn được lưu vào Firebase
- Google Sheets sẽ không được cập nhật khi offline
- Khi có kết nối trở lại, chỉ dữ liệu mới được gửi lên Google Sheets

## 6. Kiểm tra và debug

### 6.1. Log trên Serial Monitor

ESP32 sẽ hiển thị các thông tin sau trên Serial Monitor:

```
----- Bắt đầu gửi dữ liệu lên Google Sheets -----
📊 Sinh viên: Phúc du (ID: **********, RFID: F7C2453)
📅 Ngày: 20240510 | Vào: 08:30:15 | Ra: | Trạng thái: present
📤 Đang gửi dữ liệu lên Google Sheets...
📊 Mã phản hồi HTTP: 200
✅ Gửi dữ liệu lên Google Sheets thành công!
```

### 6.2. Kiểm tra trên Google Sheets

1. Mở spreadsheet với ID: `1oPFi8-iWNw-BIFbXYCpJq5wcmyR_DeHbpV7azTgMRBQ`
2. Kiểm tra sheet DIEMDANH để xem dữ liệu điểm danh mới
3. Kiểm tra sheet DANHSACH để xem danh sách sinh viên

## 7. Test với script mô phỏng

### 7.1. Chạy script điểm danh

```bash
cd hardware-simulator
node simulate-attendance.js checkin F7C2453
```

### 7.2. Kết quả mong đợi

- Dữ liệu được cập nhật vào Firebase
- ESP32 nhận thông tin và tự động cập nhật Google Sheets
- Có thể kiểm tra trực tiếp trên Google Sheets

## 8. Lưu ý quan trọng

### 8.1. Giới hạn API

- Google Sheets API có giới hạn số lượng request
- Nên tránh gửi quá nhiều request trong thời gian ngắn

### 8.2. Bảo mật

- API Key được sử dụng chung với Firebase
- Cần đảm bảo API Key không bị lộ ra ngoài

### 8.3. Đồng bộ dữ liệu

- Google Sheets chỉ được cập nhật khi có kết nối internet
- Firebase vẫn là nguồn dữ liệu chính và đáng tin cậy nhất
