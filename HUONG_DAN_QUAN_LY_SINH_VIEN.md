# Hướng dẫn quản lý danh sách sinh viên

Hệ thống cung cấp hai cách để quản lý danh sách sinh viên:
1. Thông qua ứng dụng di động
2. Thông qua script trên máy tính

## 1. Quản lý sinh viên thông qua ứng dụng di động

Ứng dụng di động cung cấp giao diện đồ họa để quản lý danh sách sinh viên một cách trực quan.

### Truy cập màn hình quản lý sinh viên

1. Mở ứng dụng di động
2. Chuyển đến tab "Cài đặt"
3. <PERSON><PERSON><PERSON> "Quản lý sinh viên"

### Các chức năng có sẵn

- **Xem danh sách sinh viên**: Danh sách sinh viên sẽ được hiển thị khi bạn vào màn hình quản lý sinh viên
- **Tìm kiếm sinh viên**: Sử dụng ô tìm kiếm ở phía trên để lọc sinh viên theo tên, mã sinh viên, lớp, ngành hoặc mã RFID
- **Thêm sinh viên mới**: Nhấn nút "+" ở góc trên bên phải để thêm sinh viên mới
- **Sửa thông tin sinh viên**: Nhấn nút "Sửa" (biểu tượng bút chì) bên cạnh sinh viên để sửa thông tin
- **Xóa sinh viên**: Nhấn nút "Xóa" (biểu tượng thùng rác) bên cạnh sinh viên để xóa

### Thêm sinh viên mới

Khi thêm sinh viên mới, bạn cần nhập các thông tin sau:
- **Mã RFID**: Mã định danh của thẻ RFID (bắt buộc)
- **Tên sinh viên**: Họ và tên đầy đủ của sinh viên (bắt buộc)
- **Mã sinh viên**: Mã số sinh viên (bắt buộc)
- **Lớp**: Lớp học của sinh viên (bắt buộc)
- **Ngành**: Ngành học của sinh viên (bắt buộc)

Lưu ý: Mã RFID phải là duy nhất và không thể thay đổi sau khi đã tạo.

## 2. Quản lý sinh viên thông qua script

Hệ thống cung cấp script `create-students.js` trong thư mục `hardware-simulator` để quản lý danh sách sinh viên thông qua dòng lệnh.

### Chuẩn bị môi trường

Trước khi sử dụng script, bạn cần cài đặt Node.js và các thư viện cần thiết:

1. Cài đặt Node.js từ [nodejs.org](https://nodejs.org/)
2. Mở terminal và di chuyển đến thư mục `hardware-simulator`
3. Chạy lệnh `npm install` để cài đặt các thư viện cần thiết

### Các lệnh có sẵn

Script hỗ trợ các lệnh sau:

#### Tạo file CSV mẫu

```bash
node create-students.js template [tên_file_csv]
```

Lệnh này sẽ tạo một file CSV mẫu với cấu trúc đúng để bạn có thể chỉnh sửa và thêm sinh viên. Nếu không chỉ định tên file, mặc định sẽ là `students_template.csv`.

#### Tạo danh sách sinh viên mẫu

```bash
node create-students.js create
```

Lệnh này sẽ tạo một danh sách 10 sinh viên mẫu trên Firebase.

#### Import sinh viên từ file CSV

```bash
node create-students.js import [đường_dẫn_file_csv]
```

Lệnh này sẽ đọc file CSV và import danh sách sinh viên lên Firebase. File CSV phải có cấu trúc như sau:

```
rfidId,name,studentId,class,major
A1B2C3D4,Nguyễn Văn A,2021607001,2021DHKTMT01,KTMT
B2C3D4E5,Trần Thị B,2021607002,2021DHKTMT01,KTMT
...
```

#### Kiểm tra danh sách sinh viên

```bash
node create-students.js check
```

Lệnh này sẽ hiển thị danh sách sinh viên hiện có trên Firebase.

#### Xóa tất cả sinh viên

```bash
node create-students.js delete
```

Lệnh này sẽ xóa tất cả sinh viên trên Firebase. Cẩn thận khi sử dụng lệnh này!

### Cấu trúc file CSV

File CSV để import sinh viên phải có cấu trúc như sau:

```
rfidId,name,studentId,class,major
A1B2C3D4,Nguyễn Văn A,2021607001,2021DHKTMT01,KTMT
B2C3D4E5,Trần Thị B,2021607002,2021DHKTMT01,KTMT
...
```

Trong đó:
- **rfidId**: Mã định danh của thẻ RFID (bắt buộc, duy nhất)
- **name**: Họ và tên đầy đủ của sinh viên (bắt buộc)
- **studentId**: Mã số sinh viên (bắt buộc)
- **class**: Lớp học của sinh viên (bắt buộc)
- **major**: Ngành học của sinh viên (bắt buộc)

Lưu ý:
- Dòng đầu tiên phải là tiêu đề như mẫu trên
- Các trường phải được phân cách bằng dấu phẩy (,)
- Không được có dấu phẩy trong các giá trị

## 3. Lưu ý quan trọng

- Mã RFID phải là duy nhất và được sử dụng làm khóa chính trong cơ sở dữ liệu
- Khi sinh viên quẹt thẻ RFID, hệ thống sẽ tìm kiếm sinh viên theo mã RFID trong cơ sở dữ liệu
- Nếu mã RFID không tồn tại trong cơ sở dữ liệu, hệ thống sẽ không ghi nhận điểm danh
- Bạn có thể sử dụng cả hai cách (ứng dụng di động và script) để quản lý sinh viên, tùy theo nhu cầu
