// Light Theme (Trắng - <PERSON>anh lá)
export const lightTheme = {
  dark: false,
  colors: {
    primary: '#4CAF50',       // Xanh lá
    background: '#FFFFFF',    // Trắng
    card: '#F5F5F5',          // Xám nhạt
    text: '#212121',          // Đen
    border: '#E0E0E0',        // Xám
    notification: '#F44336',  // Đỏ
    accent: '#8BC34A',        // Xanh lá nhạt
    surface: '#FFFFFF',       // Trắng
    error: '#F44336',         // Đỏ
    warning: '#FFC107',       // Vàng
    success: '#4CAF50',       // Xanh lá
    info: '#2196F3',          // Xanh dương
  },
  fonts: {
    regular: {
      fontFamily: 'System',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
    bold: {
      fontFamily: 'System',
      fontWeight: 'bold',
    },
  },
  roundness: 8,
  animation: {
    scale: 1.0,
  },
};

// Dark Theme (Xanh dương - Đen)
export const darkTheme = {
  dark: true,
  colors: {
    primary: '#2196F3',       // Xanh dương
    background: '#121212',    // Đen
    card: '#1E1E1E',          // Đen nhạt
    text: '#FFFFFF',          // Trắng
    border: '#333333',        // Xám đậm
    notification: '#FF5722',  // Đỏ cam
    accent: '#03A9F4',        // Xanh dương nhạt
    surface: '#1E1E1E',       // Đen nhạt
    error: '#FF5722',         // Đỏ cam
    warning: '#FFC107',       // Vàng
    success: '#4CAF50',       // Xanh lá
    info: '#2196F3',          // Xanh dương
  },
  fonts: {
    regular: {
      fontFamily: 'System',
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: 'System',
      fontWeight: '500',
    },
    light: {
      fontFamily: 'System',
      fontWeight: '300',
    },
    thin: {
      fontFamily: 'System',
      fontWeight: '100',
    },
    bold: {
      fontFamily: 'System',
      fontWeight: 'bold',
    },
  },
  roundness: 8,
  animation: {
    scale: 1.0,
  },
};
